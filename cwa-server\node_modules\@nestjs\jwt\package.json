{"name": "@nestjs/jwt", "version": "11.0.0", "description": "Nest - modern, fast, powerful node.js web framework (@jwt)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"format": "prettier --write \"**/*.ts\"", "lint": "eslint \"lib/**/*.ts\" --fix", "test": "jest --config=jest.json", "test:watch": "jest --config=jest.json --watch", "test:coverage": "jest --config=jest.json --coverage --coverageDirectory=coverage", "build": "rm -rf dist && tsc -p tsconfig.build.json", "precommit": "lint-staged", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prerelease": "npm run build", "release": "release-it", "prepare": "husky"}, "peerDependencies": {"@nestjs/common": "^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0"}, "devDependencies": {"@commitlint/cli": "19.6.1", "@commitlint/config-angular": "19.7.0", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@nestjs/common": "11.0.1", "@nestjs/core": "11.0.1", "@nestjs/testing": "11.0.1", "@types/jest": "29.5.14", "@types/node": "22.10.7", "eslint": "9.18.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.2", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.4.1", "prettier": "3.4.2", "reflect-metadata": "0.2.2", "release-it": "18.1.1", "rxjs": "7.8.1", "ts-jest": "29.2.5", "typescript": "5.7.3", "typescript-eslint": "8.20.0"}, "dependencies": {"@types/jsonwebtoken": "9.0.7", "jsonwebtoken": "9.0.2"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/jwt"}}