{"version": 3, "file": "util.test.js", "names": ["describe", "it", "thrower", "limit", "should", "trimAfterFirstMatch", "equal", "startsWith", "endsWith"], "sources": ["../../source/findNumbers/util.test.js"], "sourcesContent": ["import {\r\n\tlimit,\r\n\ttrimAfterFirstMatch,\r\n\tstartsWith,\r\n\tendsWith\r\n} from './util.js'\r\n\r\ndescribe('findNumbers/util', () =>\r\n{\r\n\tit('should generate regexp limit', () =>\r\n\t{\r\n\t\tlet thrower = () => limit(1, 0)\r\n\t\tthrower.should.throw()\r\n\r\n\t\tthrower = () => limit(-1, 1)\r\n\t\tthrower.should.throw()\r\n\r\n\t\tthrower = () => limit(0, 0)\r\n\t\tthrower.should.throw()\r\n\t})\r\n\r\n\tit('should trimAfterFirstMatch', () =>\r\n\t{\r\n\t\ttrimAfterFirstMatch(/\\d/, 'abc123').should.equal('abc')\r\n\t\ttrimAfterFirstMatch(/\\d/, 'abc').should.equal('abc')\r\n\t})\r\n\r\n\tit('should determine if a string starts with a substring', () =>\r\n\t{\r\n\t\tstartsWith('𐍈123', '𐍈').should.equal(true)\r\n\t\tstartsWith('1𐍈', '𐍈').should.equal(false)\r\n\t})\r\n\r\n\tit('should determine if a string ends with a substring', () =>\r\n\t{\r\n\t\tendsWith('123𐍈', '𐍈').should.equal(true)\r\n\t\tendsWith('𐍈1', '𐍈').should.equal(false)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAOAA,QAAQ,CAAC,kBAAD,EAAqB,YAC7B;EACCC,EAAE,CAAC,8BAAD,EAAiC,YACnC;IACC,IAAIC,OAAO,GAAG;MAAA,OAAM,IAAAC,WAAA,EAAM,CAAN,EAAS,CAAT,CAAN;IAAA,CAAd;;IACAD,OAAO,CAACE,MAAR;;IAEAF,OAAO,GAAG;MAAA,OAAM,IAAAC,WAAA,EAAM,CAAC,CAAP,EAAU,CAAV,CAAN;IAAA,CAAV;;IACAD,OAAO,CAACE,MAAR;;IAEAF,OAAO,GAAG;MAAA,OAAM,IAAAC,WAAA,EAAM,CAAN,EAAS,CAAT,CAAN;IAAA,CAAV;;IACAD,OAAO,CAACE,MAAR;EACA,CAVC,CAAF;EAYAH,EAAE,CAAC,4BAAD,EAA+B,YACjC;IACC,IAAAI,yBAAA,EAAoB,IAApB,EAA0B,QAA1B,EAAoCD,MAApC,CAA2CE,KAA3C,CAAiD,KAAjD;IACA,IAAAD,yBAAA,EAAoB,IAApB,EAA0B,KAA1B,EAAiCD,MAAjC,CAAwCE,KAAxC,CAA8C,KAA9C;EACA,CAJC,CAAF;EAMAL,EAAE,CAAC,sDAAD,EAAyD,YAC3D;IACC,IAAAM,gBAAA,EAAW,OAAX,EAAoB,IAApB,EAA0BH,MAA1B,CAAiCE,KAAjC,CAAuC,IAAvC;IACA,IAAAC,gBAAA,EAAW,KAAX,EAAkB,IAAlB,EAAwBH,MAAxB,CAA+BE,KAA/B,CAAqC,KAArC;EACA,CAJC,CAAF;EAMAL,EAAE,CAAC,oDAAD,EAAuD,YACzD;IACC,IAAAO,cAAA,EAAS,OAAT,EAAkB,IAAlB,EAAwBJ,MAAxB,CAA+BE,KAA/B,CAAqC,IAArC;IACA,IAAAE,cAAA,EAAS,KAAT,EAAgB,IAAhB,EAAsBJ,MAAtB,CAA6BE,KAA7B,CAAmC,KAAnC;EACA,CAJC,CAAF;AAKA,CA/BO,CAAR"}