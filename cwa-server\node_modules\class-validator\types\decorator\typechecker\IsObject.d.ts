import { ValidationOptions } from '../ValidationOptions';
export declare const IS_OBJECT = "isObject";
/**
 * Checks if the value is valid Object.
 * Returns false if the value is not an object.
 */
export declare function isObject<T = object>(value: unknown): value is T;
/**
 * Checks if the value is valid Object.
 * Returns false if the value is not an object.
 */
export declare function IsObject(validationOptions?: ValidationOptions): PropertyDecorator;
