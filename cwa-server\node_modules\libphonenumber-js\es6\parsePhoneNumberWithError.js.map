{"version": 3, "file": "parsePhoneNumberWithError.js", "names": ["parsePhoneNumberWithError_", "normalizeArguments", "parsePhoneNumberWithError", "arguments", "text", "options", "metadata"], "sources": ["../source/parsePhoneNumberWithError.js"], "sourcesContent": ["import parsePhoneNumberWithError_ from './parsePhoneNumberWithError_.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function parsePhoneNumberWithError() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumberWithError_(text, options, metadata)\r\n}"], "mappings": "AAAA,OAAOA,0BAAP,MAAuC,iCAAvC;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AAEA,eAAe,SAASC,yBAAT,GAAqC;EACnD,0BAAoCD,kBAAkB,CAACE,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAON,0BAA0B,CAACI,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAAjC;AACA"}