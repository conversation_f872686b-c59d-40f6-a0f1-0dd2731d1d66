import { ValidationOptions } from '../ValidationOptions';
export declare const IS_SEM_VER = "isSemVer";
/**
 * Check if the string is a Semantic Versioning Specification (SemVer).
 * If given value is not a string, then it returns false.
 */
export declare function isSemVer(value: unknown): boolean;
/**
 * Check if the string is a Semantic Versioning Specification (SemVer).
 * If given value is not a string, then it returns false.
 */
export declare function IsSemVer(validationOptions?: ValidationOptions): PropertyDecorator;
