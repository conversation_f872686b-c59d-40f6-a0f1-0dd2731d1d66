{"version": 3, "file": "Validator.js", "sourceRoot": "", "sources": ["../../../src/validation/Validator.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAG1D;;GAEG;AACH,MAAM,OAAO,SAAS;IAepB;;OAEG;IACH,QAAQ,CACN,kBAAmC,EACnC,yBAAqD,EACrD,qBAAwC;QAExC,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,yBAAyB,EAAE,qBAAqB,CAAC,CAAC;IACjG,CAAC;IAYD;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,kBAAmC,EACnC,yBAAqD,EACrD,qBAAwC;QAExC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,yBAAyB,EAAE,qBAAqB,CAAC,CAAC;QAC7G,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IAaD;;OAEG;IACH,YAAY,CACV,kBAAmC,EACnC,yBAAqD,EACrD,qBAAwC;QAExC,MAAM,MAAM,GAAG,OAAO,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAE,yBAAoC,CAAC,CAAC,CAAC,kBAAkB,CAAC;QACnH,MAAM,OAAO,GACX,OAAO,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAE,yBAA+C,CAAC;QACpH,MAAM,MAAM,GAAG,OAAO,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;QAEvF,MAAM,QAAQ,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACvD,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACvC,MAAM,gBAAgB,GAAsB,EAAE,CAAC;QAC/C,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACnD,OAAO,QAAQ,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;IAED,4EAA4E;IAC5E,qBAAqB;IACrB,4EAA4E;IAC5E;;;OAGG;IACK,YAAY,CAClB,kBAAmC,EACnC,yBAAqD,EACrD,qBAAwC;QAExC,MAAM,MAAM,GAAG,OAAO,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAE,yBAAoC,CAAC,CAAC,CAAC,kBAAkB,CAAC;QACnH,MAAM,OAAO,GACX,OAAO,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAE,yBAA+C,CAAC;QACpH,MAAM,MAAM,GAAG,OAAO,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;QAEvF,MAAM,QAAQ,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACvD,MAAM,gBAAgB,GAAsB,EAAE,CAAC;QAC/C,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;QAEnD,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACtD,OAAO,QAAQ,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["import { ValidationError } from './ValidationError';\nimport { ValidatorOptions } from './ValidatorOptions';\nimport { ValidationExecutor } from './ValidationExecutor';\nimport { ValidationOptions } from '../decorator/ValidationOptions';\n\n/**\n * Validator performs validation of the given object based on its metadata.\n */\nexport class Validator {\n  // -------------------------------------------------------------------------\n  // Public Methods\n  // -------------------------------------------------------------------------\n\n  /**\n   * Performs validation of the given object based on decorators used in given object class.\n   */\n  validate(object: object, options?: ValidatorOptions): Promise<ValidationError[]>;\n\n  /**\n   * Performs validation of the given object based on validation schema.\n   */\n  validate(schemaName: string, object: object, options?: ValidatorOptions): Promise<ValidationError[]>;\n\n  /**\n   * Performs validation of the given object based on decorators or validation schema.\n   */\n  validate(\n    objectOrSchemaName: object | string,\n    objectOrValidationOptions: object | ValidationOptions,\n    maybeValidatorOptions?: ValidatorOptions\n  ): Promise<ValidationError[]> {\n    return this.coreValidate(objectOrSchemaName, objectOrValidationOptions, maybeValidatorOptions);\n  }\n\n  /**\n   * Performs validation of the given object based on decorators used in given object class and reject on error.\n   */\n  validateOrReject(object: object, options?: ValidatorOptions): Promise<void>;\n\n  /**\n   * Performs validation of the given object based on validation schema and reject on error.\n   */\n  validateOrReject(schemaName: string, object: object, options?: ValidatorOptions): Promise<void>;\n\n  /**\n   * Performs validation of the given object based on decorators or validation schema and reject on error.\n   */\n  async validateOrReject(\n    objectOrSchemaName: object | string,\n    objectOrValidationOptions: object | ValidationOptions,\n    maybeValidatorOptions?: ValidatorOptions\n  ): Promise<void> {\n    const errors = await this.coreValidate(objectOrSchemaName, objectOrValidationOptions, maybeValidatorOptions);\n    if (errors.length) return Promise.reject(errors);\n  }\n\n  /**\n   * Performs validation of the given object based on decorators used in given object class.\n   * NOTE: This method completely ignores all async validations.\n   */\n  validateSync(object: object, options?: ValidatorOptions): ValidationError[];\n\n  /**\n   * Performs validation of the given object based on validation schema.\n   */\n  validateSync(schemaName: string, object: object, options?: ValidatorOptions): ValidationError[];\n\n  /**\n   * Performs validation of the given object based on decorators or validation schema.\n   */\n  validateSync(\n    objectOrSchemaName: object | string,\n    objectOrValidationOptions: object | ValidationOptions,\n    maybeValidatorOptions?: ValidatorOptions\n  ): ValidationError[] {\n    const object = typeof objectOrSchemaName === 'string' ? (objectOrValidationOptions as object) : objectOrSchemaName;\n    const options =\n      typeof objectOrSchemaName === 'string' ? maybeValidatorOptions : (objectOrValidationOptions as ValidationOptions);\n    const schema = typeof objectOrSchemaName === 'string' ? objectOrSchemaName : undefined;\n\n    const executor = new ValidationExecutor(this, options);\n    executor.ignoreAsyncValidations = true;\n    const validationErrors: ValidationError[] = [];\n    executor.execute(object, schema, validationErrors);\n    return executor.stripEmptyErrors(validationErrors);\n  }\n\n  // -------------------------------------------------------------------------\n  // Private Properties\n  // -------------------------------------------------------------------------\n  /**\n   * Performs validation of the given object based on decorators or validation schema.\n   * Common method for `validateOrReject` and `validate` methods.\n   */\n  private coreValidate(\n    objectOrSchemaName: object | string,\n    objectOrValidationOptions: object | ValidationOptions,\n    maybeValidatorOptions?: ValidatorOptions\n  ): Promise<ValidationError[]> {\n    const object = typeof objectOrSchemaName === 'string' ? (objectOrValidationOptions as object) : objectOrSchemaName;\n    const options =\n      typeof objectOrSchemaName === 'string' ? maybeValidatorOptions : (objectOrValidationOptions as ValidationOptions);\n    const schema = typeof objectOrSchemaName === 'string' ? objectOrSchemaName : undefined;\n\n    const executor = new ValidationExecutor(this, options);\n    const validationErrors: ValidationError[] = [];\n    executor.execute(object, schema, validationErrors);\n\n    return Promise.all(executor.awaitingPromises).then(() => {\n      return executor.stripEmptyErrors(validationErrors);\n    });\n  }\n}\n"]}