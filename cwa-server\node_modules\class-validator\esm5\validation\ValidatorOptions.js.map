{"version": 3, "file": "ValidatorOptions.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidatorOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Options passed to validator during validation.\n */\nexport interface ValidatorOptions {\n  /**\n   * If set to true then class-validator will print extra warning messages to the console when something is not right.\n   */\n  enableDebugMessages?: boolean;\n\n  /**\n   * If set to true then validator will skip validation of all properties that are undefined in the validating object.\n   */\n  skipUndefinedProperties?: boolean;\n\n  /**\n   * If set to true then validator will skip validation of all properties that are null in the validating object.\n   */\n  skipNullProperties?: boolean;\n\n  /**\n   * If set to true then validator will skip validation of all properties that are null or undefined in the validating object.\n   */\n  skipMissingProperties?: boolean;\n\n  /**\n   * If set to true validator will strip validated object of any properties that do not have any decorators.\n   *\n   * Tip: if no other decorator is suitable for your property use @Allow decorator.\n   */\n  whitelist?: boolean;\n\n  /**\n   * If set to true, instead of stripping non-whitelisted properties validator will throw an error\n   */\n  forbidNonWhitelisted?: boolean;\n\n  /**\n   * Groups to be used during validation of the object.\n   */\n  groups?: string[];\n\n  /**\n   * Set default for `always` option of decorators. Default can be overridden in decorator options.\n   */\n  always?: boolean;\n\n  /**\n   * If [groups]{@link ValidatorOptions#groups} is not given or is empty,\n   * ignore decorators with at least one group.\n   */\n  strictGroups?: boolean;\n\n  /**\n   * If set to true, the validation will not use default messages.\n   * Error message always will be undefined if its not explicitly set.\n   */\n  dismissDefaultMessages?: boolean;\n\n  /**\n   * ValidationError special options.\n   */\n  validationError?: {\n    /**\n     * Indicates if target should be exposed in ValidationError.\n     */\n    target?: boolean;\n\n    /**\n     * Indicates if validated value should be exposed in ValidationError.\n     */\n    value?: boolean;\n  };\n\n  /**\n   * Fails validation for objects unknown to class-validator. Defaults to true.\n   *\n   * For instance, since a plain empty object has no annotations used for validation:\n   * - `validate({})` // fails.\n   * - `validate({}, { forbidUnknownValues: true })` // fails.\n   * - `validate({}, { forbidUnknownValues: false })` // passes.\n   * - `validate(new SomeAnnotatedEmptyClass(), { forbidUnknownValues: true })` // passes.\n   */\n  forbidUnknownValues?: boolean;\n\n  /**\n   * When set to true, validation of the given property will stop after encountering the first error. Defaults to false.\n   */\n  stopAtFirstError?: boolean;\n}\n"]}