import { ValidationOptions } from '../ValidationOptions';
export declare const IS_CREDIT_CARD = "isCreditCard";
/**
 * Checks if the string is a credit card.
 * If given value is not a string, then it returns false.
 */
export declare function isCreditCard(value: unknown): boolean;
/**
 * Checks if the string is a credit card.
 * If given value is not a string, then it returns false.
 */
export declare function IsCreditCard(validationOptions?: ValidationOptions): PropertyDecorator;
