{"version": 3, "file": "searchPhoneNumbersInText.js", "names": ["PhoneNumberMatcher", "normalizeArguments", "searchPhoneNumbersInText", "arguments", "text", "options", "metadata", "matcher", "v2", "Symbol", "iterator", "next", "hasNext", "done", "value"], "sources": ["../source/searchPhoneNumbersInText.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function searchPhoneNumbersInText() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, { ...options, v2: true }, metadata)\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AAEA,eAAe,SAASC,wBAAT,GAAoC;EAClD,0BAAoCD,kBAAkB,CAACE,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,IAAMC,OAAO,GAAG,IAAIP,kBAAJ,CAAuBI,IAAvB,kCAAkCC,OAAlC;IAA2CG,EAAE,EAAE;EAA/C,IAAuDF,QAAvD,CAAhB;EACA,2BACEG,MAAM,CAACC,QADT,cACqB;IACnB,OAAO;MACHC,IAAI,EAAE,gBAAM;QACX,IAAIJ,OAAO,CAACK,OAAR,EAAJ,EAAuB;UACzB,OAAO;YACNC,IAAI,EAAE,KADA;YAENC,KAAK,EAAEP,OAAO,CAACI,IAAR;UAFD,CAAP;QAIA;;QACD,OAAO;UACNE,IAAI,EAAE;QADA,CAAP;MAGG;IAXE,CAAP;EAaA,CAfF;AAiBA"}