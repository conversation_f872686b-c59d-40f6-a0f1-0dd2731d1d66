{"version": 3, "file": "ValidationMetadataArgs.js", "sourceRoot": "", "sources": ["../../../src/metadata/ValidationMetadataArgs.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ValidationOptions } from '../decorator/ValidationOptions';\n\n/**\n * Constructor arguments for ValidationMetadata class.\n */\nexport interface ValidationMetadataArgs {\n  /**\n   * Validation type.\n   */\n  type: string;\n\n  /**\n   * Validator name.\n   */\n  name?: string;\n\n  /**\n   * Object that is used to be validated.\n   */\n  target: Function | string;\n\n  /**\n   * Property of the object to be validated.\n   */\n  propertyName: string;\n\n  /**\n   * Constraint class that performs validation. Used only for custom validations.\n   */\n  constraintCls?: Function;\n\n  /**\n   * Array of constraints of this validation.\n   */\n  constraints?: any[];\n\n  /**\n   * Validation options.\n   */\n  validationOptions?: ValidationOptions;\n\n  /**\n   * Extra options specific to validation type.\n   */\n  validationTypeOptions?: any;\n}\n"]}