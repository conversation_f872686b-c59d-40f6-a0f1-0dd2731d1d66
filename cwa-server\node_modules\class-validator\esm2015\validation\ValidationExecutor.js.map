{"version": 3, "file": "ValidationExecutor.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidationExecutor.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAGpD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAGpD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAEjE;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAc7B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAoB,SAAoB,EAAU,gBAAmC;QAAjE,cAAS,GAAT,SAAS,CAAW;QAAU,qBAAgB,GAAhB,gBAAgB,CAAmB;QAjBrF,4EAA4E;QAC5E,aAAa;QACb,4EAA4E;QAE5E,qBAAgB,GAAmB,EAAE,CAAC;QACtC,2BAAsB,GAAY,KAAK,CAAC;QAExC,4EAA4E;QAC5E,qBAAqB;QACrB,4EAA4E;QAEpE,oBAAe,GAAG,kBAAkB,EAAE,CAAC;IAMyC,CAAC;IAEzF,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,OAAO,CAAC,MAAc,EAAE,YAAoB,EAAE,gBAAmC;;QAC/E;;;;;WAKG;QACH,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAqB,IAAI,CAAA,MAAA,IAAI,CAAC,gBAAgB,0CAAE,mBAAmB,MAAK,IAAI,EAAE,CAAC;YACvG,OAAO,CAAC,IAAI,CACV,wGAAwG;gBACtG,8HAA8H;gBAC9H,0FAA0F,CAC7F,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QAChF,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;QAC5F,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;QAChF,oGAAoG;QACpG,MAAM,mBAAmB,GACvB,CAAA,MAAA,IAAI,CAAC,gBAAgB,0CAAE,mBAAmB,MAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,KAAK,CAAC;QAElH,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,4BAA4B,CACvE,MAAM,CAAC,WAAW,EAClB,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,MAAM,CACP,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAEnF,IAAI,mBAAmB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YACnD,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;YAE9C,IACE,CAAC,IAAI,CAAC,gBAAgB;gBACtB,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe;gBACtC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,KAAK,SAAS;gBAC1D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,KAAK,IAAI;gBAErD,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;YAElC,eAAe,CAAC,KAAK,GAAG,SAAS,CAAC;YAClC,eAAe,CAAC,QAAQ,GAAG,SAAS,CAAC;YACrC,eAAe,CAAC,QAAQ,GAAG,EAAE,CAAC;YAC9B,eAAe,CAAC,WAAW,GAAG,EAAE,YAAY,EAAE,sDAAsD,EAAE,CAAC;YAEvG,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAEvC,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;QAE7D,qBAAqB;QACrB,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACnD,MAAM,KAAK,GAAI,MAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC,MAAM,CAC5D,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,UAAU,CACzD,CAAC;YACF,MAAM,SAAS,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC,MAAM,CACrD,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,SAAS,CACxG,CAAC;YAEF,IACE,KAAK,YAAY,OAAO;gBACxB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,kBAAkB,CAAC,EAChF,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CACxB,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;oBACzB,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;gBAC9G,CAAC,CAAC,CACH,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;YACtG,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,CACP,MAAW,EACX,gBAAkE,EAClE,gBAAmC;QAEnC,MAAM,oBAAoB,GAAa,EAAE,CAAC;QAE1C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACzC,uCAAuC;YACvC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,gBAAgB,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC;gBAChF,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;gBACxE,eAAe;gBACf,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACtC,MAAM,eAAe,GAAoB,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;oBAC1G,eAAe,CAAC,WAAW,GAAG,EAAE,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,YAAY,QAAQ,mBAAmB,EAAE,CAAC;oBACvG,eAAe,CAAC,QAAQ,GAAG,SAAS,CAAC;oBACrC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,+BAA+B;gBAC/B,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,MAAyB;QACxC,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,OAAO,KAAK,CAAC;gBACf,CAAC;qBAAM,CAAC;oBACN,OAAO,KAAK,CAAC,WAAW,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAEpE,kBAAkB,CACxB,MAAW,EACX,KAAU,EACV,YAAoB,EACpB,gBAAsC,EACtC,SAA+B,EAC/B,gBAAmC;QAEnC,MAAM,yBAAyB,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,CAAC,CAAC;QACpH,MAAM,yBAAyB,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,CAAC,CAAC;QACpH,MAAM,8BAA8B,GAAG,SAAS,CAAC,MAAM,CACrD,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,sBAAsB,CACrE,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QAClF,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvC,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,EAAE,8BAA8B,CAAC,CAAC;QAC/F,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,2IAA2I;QAC3I,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;QACzE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAEnE,IAAI,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,KAAK,IAAI,EAAE,CAAC;YAC3G,OAAO;QACT,CAAC;QAED,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;YACjG,OAAO;QACT,CAAC;QAED,IACE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;YACvC,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,KAAK,IAAI,EACpD,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,yBAAyB,EAAE,eAAe,CAAC,CAAC;QAClF,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,yBAAyB,EAAE,eAAe,CAAC,CAAC;QAE1E,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,yBAAyB,EAAE,eAAe,CAAC,CAAC;IAC9E,CAAC;IAEO,uBAAuB,CAAC,MAAc,EAAE,KAAU,EAAE,YAAoB;QAC9E,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAE9C,IACE,CAAC,IAAI,CAAC,gBAAgB;YACtB,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe;YACtC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,KAAK,SAAS;YAC1D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,KAAK,IAAI;YAErD,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;QAElC,IACE,CAAC,IAAI,CAAC,gBAAgB;YACtB,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe;YACtC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,KAAK,KAAK,SAAS;YACzD,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,KAAK,KAAK,IAAI;YAEpD,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;QAEhC,eAAe,CAAC,QAAQ,GAAG,YAAY,CAAC;QACxC,eAAe,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC9B,eAAe,CAAC,WAAW,GAAG,EAAE,CAAC;QAEjC,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,sBAAsB,CAAC,MAAc,EAAE,KAAU,EAAE,SAA+B;QACxF,OAAO,SAAS;aACb,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;aACvD,MAAM,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,OAAO,IAAI,OAAO,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEO,iBAAiB,CAAC,MAAc,EAAE,KAAU,EAAE,SAA+B,EAAE,KAAsB;QAC3G,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,CAAC,eAAe,CAAC,6BAA6B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,EAAE;gBAC5G,IAAI,wBAAwB,CAAC,KAAK,IAAI,IAAI,CAAC,sBAAsB;oBAAE,OAAO;gBAC1E,IACE,IAAI,CAAC,gBAAgB;oBACrB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;oBACtC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;oBAE/C,OAAO;gBAET,MAAM,mBAAmB,GAAwB;oBAC/C,UAAU,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAE,MAAM,CAAC,WAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;oBAC7E,QAAQ,EAAE,QAAQ,CAAC,YAAY;oBAC/B,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,KAAK;oBACZ,WAAW,EAAE,QAAQ,CAAC,WAAW;iBAClC,CAAC;gBAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,YAAY,GAAG,CAAC,EAAE,CAAC;oBAC9F,MAAM,cAAc,GAAG,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;oBAC9F,IAAI,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC;wBAC9B,MAAM,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;4BAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;gCACb,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC;gCACtG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;gCAClC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oCACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;wCACpB,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;oCACtB,CAAC;oCACD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;gCACrF,CAAC;4BACH,CAAC;wBACH,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACtC,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,cAAc,EAAE,CAAC;4BACpB,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC;4BACtG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;wBACpC,CAAC;oBACH,CAAC;oBAED,OAAO;gBACT,CAAC;gBAED,iCAAiC;gBACjC,MAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;gBACzC,oDAAoD;gBACpD,MAAM,kBAAkB,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAC1D,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAC1E,CAAC;gBACF,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,iBAA6C,EAAE,EAAE,CAClG,SAAS,CAAC,iBAAiB,CAAC,CAC7B,CAAC;gBAEF,IAAI,iBAAiB,EAAE,CAAC;oBACtB,gEAAgE;oBAChE,MAAM,uBAAuB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,iBAA6C,EAAE,EAAE,CACvG,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CACtF,CAAC;oBACF,MAAM,gCAAgC,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAChF,CAAC,mBAA8B,EAAE,EAAE;wBACjC,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,OAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;wBAClF,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BACtB,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC;4BACtG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;4BAClC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gCACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;oCACpB,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;gCACtB,CAAC;gCACD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;4BACrF,CAAC;wBACH,CAAC;oBACH,CAAC,CACF,CAAC;oBAEF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;oBAE7D,OAAO;gBACT,CAAC;gBAED,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,OAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;gBACjF,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC;oBACtG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;gBACpC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAAU,EAAE,SAA+B,EAAE,KAAsB;QAC3F,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBAChH,OAAO;YACT,CAAC;iBAAM,IACL,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;gBACtC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAC/C,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,YAAY,GAAG,EAAE,CAAC;gBACzE,+GAA+G;gBAC/G,MAAM,cAAc,GAAG,KAAK,YAAY,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACxE,cAAc,CAAC,OAAO,CAAC,CAAC,QAAa,EAAE,KAAU,EAAE,EAAE;oBACnD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAC5F,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,KAAK,YAAY,MAAM,EAAE,CAAC;gBACnC,MAAM,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;gBAClG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,MAAgB,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAC/F,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,MAAc,EAAE,KAAU,EAAE,SAA+B,EAAE,KAAsB;QACrG,OAAO,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAClC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,gBAAgB,CAAC;gBACrB,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,EAAE,CAAC;oBACxD,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,6BAA6B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;oBACrG,gBAAgB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBAC1C,CAAC;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;gBAEhE,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACpB,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACtB,CAAC;oBAED,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACrF,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAC3B,MAAc,EACd,KAAU,EACV,QAA4B,EAC5B,uBAA4C;QAE5C,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAE,MAAM,CAAC,WAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QACrF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;QACvE,MAAM,mBAAmB,GAAwB;YAC/C,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,QAAQ,CAAC,YAAY;YAC/B,MAAM,EAAE,MAAM;YACd,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE,QAAQ,CAAC,WAAW;SAClC,CAAC;QAEF,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;QACrC,IACE,CAAC,QAAQ,CAAC,OAAO;YACjB,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,EACpG,CAAC;YACD,IAAI,uBAAuB,IAAI,uBAAuB,CAAC,QAAQ,CAAC,cAAc,YAAY,QAAQ,EAAE,CAAC;gBACnG,OAAO,GAAG,uBAAuB,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAG,eAAe,CAAC,2BAA2B,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QAChG,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IAC/B,CAAC;IAEO,iBAAiB,CAAC,QAA4B,EAAE,uBAA4C;QAClG,MAAM,IAAI,GAAG,uBAAuB,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;QACpH,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "sourcesContent": ["import { Validator } from './Validator';\nimport { ValidationError } from './ValidationError';\nimport { ValidationMetadata } from '../metadata/ValidationMetadata';\nimport { ValidatorOptions } from './ValidatorOptions';\nimport { ValidationTypes } from './ValidationTypes';\nimport { ConstraintMetadata } from '../metadata/ConstraintMetadata';\nimport { ValidationArguments } from './ValidationArguments';\nimport { ValidationUtils } from './ValidationUtils';\nimport { isPromise, convertToArray } from '../utils';\nimport { getMetadataStorage } from '../metadata/MetadataStorage';\n\n/**\n * Executes validation over given object.\n */\nexport class ValidationExecutor {\n  // -------------------------------------------------------------------------\n  // Properties\n  // -------------------------------------------------------------------------\n\n  awaitingPromises: Promise<any>[] = [];\n  ignoreAsyncValidations: boolean = false;\n\n  // -------------------------------------------------------------------------\n  // Private Properties\n  // -------------------------------------------------------------------------\n\n  private metadataStorage = getMetadataStorage();\n\n  // -------------------------------------------------------------------------\n  // Constructor\n  // -------------------------------------------------------------------------\n\n  constructor(private validator: Validator, private validatorOptions?: ValidatorOptions) {}\n\n  // -------------------------------------------------------------------------\n  // Public Methods\n  // -------------------------------------------------------------------------\n\n  execute(object: object, targetSchema: string, validationErrors: ValidationError[]): void {\n    /**\n     * If there is no metadata registered it means possibly the dependencies are not flatterned and\n     * more than one instance is used.\n     *\n     * TODO: This needs proper handling, forcing to use the same container or some other proper solution.\n     */\n    if (!this.metadataStorage.hasValidationMetaData && this.validatorOptions?.enableDebugMessages === true) {\n      console.warn(\n        `No validation metadata found. No validation will be  performed. There are multiple possible reasons:\\n` +\n          `  - There may be multiple class-validator versions installed. You will need to flatten your dependencies to fix the issue.\\n` +\n          `  - This validation runs before any file with validation decorator was parsed by NodeJS.`\n      );\n    }\n\n    const groups = this.validatorOptions ? this.validatorOptions.groups : undefined;\n    const strictGroups = (this.validatorOptions && this.validatorOptions.strictGroups) || false;\n    const always = (this.validatorOptions && this.validatorOptions.always) || false;\n    /** Forbid unknown values are turned on by default and any other value than false will enable it. */\n    const forbidUnknownValues =\n      this.validatorOptions?.forbidUnknownValues === undefined || this.validatorOptions.forbidUnknownValues !== false;\n\n    const targetMetadatas = this.metadataStorage.getTargetValidationMetadatas(\n      object.constructor,\n      targetSchema,\n      always,\n      strictGroups,\n      groups\n    );\n    const groupedMetadatas = this.metadataStorage.groupByPropertyName(targetMetadatas);\n\n    if (forbidUnknownValues && !targetMetadatas.length) {\n      const validationError = new ValidationError();\n\n      if (\n        !this.validatorOptions ||\n        !this.validatorOptions.validationError ||\n        this.validatorOptions.validationError.target === undefined ||\n        this.validatorOptions.validationError.target === true\n      )\n        validationError.target = object;\n\n      validationError.value = undefined;\n      validationError.property = undefined;\n      validationError.children = [];\n      validationError.constraints = { unknownValue: 'an unknown value was passed to the validate function' };\n\n      validationErrors.push(validationError);\n\n      return;\n    }\n\n    if (this.validatorOptions && this.validatorOptions.whitelist)\n      this.whitelist(object, groupedMetadatas, validationErrors);\n\n    // General validation\n    Object.keys(groupedMetadatas).forEach(propertyName => {\n      const value = (object as any)[propertyName];\n      const definedMetadatas = groupedMetadatas[propertyName].filter(\n        metadata => metadata.type === ValidationTypes.IS_DEFINED\n      );\n      const metadatas = groupedMetadatas[propertyName].filter(\n        metadata => metadata.type !== ValidationTypes.IS_DEFINED && metadata.type !== ValidationTypes.WHITELIST\n      );\n\n      if (\n        value instanceof Promise &&\n        metadatas.find(metadata => metadata.type === ValidationTypes.PROMISE_VALIDATION)\n      ) {\n        this.awaitingPromises.push(\n          value.then(resolvedValue => {\n            this.performValidations(object, resolvedValue, propertyName, definedMetadatas, metadatas, validationErrors);\n          })\n        );\n      } else {\n        this.performValidations(object, value, propertyName, definedMetadatas, metadatas, validationErrors);\n      }\n    });\n  }\n\n  whitelist(\n    object: any,\n    groupedMetadatas: { [propertyName: string]: ValidationMetadata[] },\n    validationErrors: ValidationError[]\n  ): void {\n    const notAllowedProperties: string[] = [];\n\n    Object.keys(object).forEach(propertyName => {\n      // does this property have no metadata?\n      if (!groupedMetadatas[propertyName] || groupedMetadatas[propertyName].length === 0)\n        notAllowedProperties.push(propertyName);\n    });\n\n    if (notAllowedProperties.length > 0) {\n      if (this.validatorOptions && this.validatorOptions.forbidNonWhitelisted) {\n        // throw errors\n        notAllowedProperties.forEach(property => {\n          const validationError: ValidationError = this.generateValidationError(object, object[property], property);\n          validationError.constraints = { [ValidationTypes.WHITELIST]: `property ${property} should not exist` };\n          validationError.children = undefined;\n          validationErrors.push(validationError);\n        });\n      } else {\n        // strip non allowed properties\n        notAllowedProperties.forEach(property => delete object[property]);\n      }\n    }\n  }\n\n  stripEmptyErrors(errors: ValidationError[]): ValidationError[] {\n    return errors.filter(error => {\n      if (error.children) {\n        error.children = this.stripEmptyErrors(error.children);\n      }\n\n      if (Object.keys(error.constraints).length === 0) {\n        if (error.children.length === 0) {\n          return false;\n        } else {\n          delete error.constraints;\n        }\n      }\n\n      return true;\n    });\n  }\n\n  // -------------------------------------------------------------------------\n  // Private Methods\n  // -------------------------------------------------------------------------\n\n  private performValidations(\n    object: any,\n    value: any,\n    propertyName: string,\n    definedMetadatas: ValidationMetadata[],\n    metadatas: ValidationMetadata[],\n    validationErrors: ValidationError[]\n  ): void {\n    const customValidationMetadatas = metadatas.filter(metadata => metadata.type === ValidationTypes.CUSTOM_VALIDATION);\n    const nestedValidationMetadatas = metadatas.filter(metadata => metadata.type === ValidationTypes.NESTED_VALIDATION);\n    const conditionalValidationMetadatas = metadatas.filter(\n      metadata => metadata.type === ValidationTypes.CONDITIONAL_VALIDATION\n    );\n\n    const validationError = this.generateValidationError(object, value, propertyName);\n    validationErrors.push(validationError);\n\n    const canValidate = this.conditionalValidations(object, value, conditionalValidationMetadatas);\n    if (!canValidate) {\n      return;\n    }\n\n    // handle IS_DEFINED validation type the special way - it should work no matter skipUndefinedProperties/skipMissingProperties is set or not\n    this.customValidations(object, value, definedMetadatas, validationError);\n    this.mapContexts(object, value, definedMetadatas, validationError);\n\n    if (value === undefined && this.validatorOptions && this.validatorOptions.skipUndefinedProperties === true) {\n      return;\n    }\n\n    if (value === null && this.validatorOptions && this.validatorOptions.skipNullProperties === true) {\n      return;\n    }\n\n    if (\n      (value === null || value === undefined) &&\n      this.validatorOptions &&\n      this.validatorOptions.skipMissingProperties === true\n    ) {\n      return;\n    }\n\n    this.customValidations(object, value, customValidationMetadatas, validationError);\n    this.nestedValidations(value, nestedValidationMetadatas, validationError);\n\n    this.mapContexts(object, value, metadatas, validationError);\n    this.mapContexts(object, value, customValidationMetadatas, validationError);\n  }\n\n  private generateValidationError(object: object, value: any, propertyName: string): ValidationError {\n    const validationError = new ValidationError();\n\n    if (\n      !this.validatorOptions ||\n      !this.validatorOptions.validationError ||\n      this.validatorOptions.validationError.target === undefined ||\n      this.validatorOptions.validationError.target === true\n    )\n      validationError.target = object;\n\n    if (\n      !this.validatorOptions ||\n      !this.validatorOptions.validationError ||\n      this.validatorOptions.validationError.value === undefined ||\n      this.validatorOptions.validationError.value === true\n    )\n      validationError.value = value;\n\n    validationError.property = propertyName;\n    validationError.children = [];\n    validationError.constraints = {};\n\n    return validationError;\n  }\n\n  private conditionalValidations(object: object, value: any, metadatas: ValidationMetadata[]): ValidationMetadata[] {\n    return metadatas\n      .map(metadata => metadata.constraints[0](object, value))\n      .reduce((resultA, resultB) => resultA && resultB, true);\n  }\n\n  private customValidations(object: object, value: any, metadatas: ValidationMetadata[], error: ValidationError): void {\n    metadatas.forEach(metadata => {\n      this.metadataStorage.getTargetValidatorConstraints(metadata.constraintCls).forEach(customConstraintMetadata => {\n        if (customConstraintMetadata.async && this.ignoreAsyncValidations) return;\n        if (\n          this.validatorOptions &&\n          this.validatorOptions.stopAtFirstError &&\n          Object.keys(error.constraints || {}).length > 0\n        )\n          return;\n\n        const validationArguments: ValidationArguments = {\n          targetName: object.constructor ? (object.constructor as any).name : undefined,\n          property: metadata.propertyName,\n          object: object,\n          value: value,\n          constraints: metadata.constraints,\n        };\n\n        if (!metadata.each || !(Array.isArray(value) || value instanceof Set || value instanceof Map)) {\n          const validatedValue = customConstraintMetadata.instance.validate(value, validationArguments);\n          if (isPromise(validatedValue)) {\n            const promise = validatedValue.then(isValid => {\n              if (!isValid) {\n                const [type, message] = this.createValidationError(object, value, metadata, customConstraintMetadata);\n                error.constraints[type] = message;\n                if (metadata.context) {\n                  if (!error.contexts) {\n                    error.contexts = {};\n                  }\n                  error.contexts[type] = Object.assign(error.contexts[type] || {}, metadata.context);\n                }\n              }\n            });\n            this.awaitingPromises.push(promise);\n          } else {\n            if (!validatedValue) {\n              const [type, message] = this.createValidationError(object, value, metadata, customConstraintMetadata);\n              error.constraints[type] = message;\n            }\n          }\n\n          return;\n        }\n\n        // convert set and map into array\n        const arrayValue = convertToArray(value);\n        // Validation needs to be applied to each array item\n        const validatedSubValues = arrayValue.map((subValue: any) =>\n          customConstraintMetadata.instance.validate(subValue, validationArguments)\n        );\n        const validationIsAsync = validatedSubValues.some((validatedSubValue: boolean | Promise<boolean>) =>\n          isPromise(validatedSubValue)\n        );\n\n        if (validationIsAsync) {\n          // Wrap plain values (if any) in promises, so that all are async\n          const asyncValidatedSubValues = validatedSubValues.map((validatedSubValue: boolean | Promise<boolean>) =>\n            isPromise(validatedSubValue) ? validatedSubValue : Promise.resolve(validatedSubValue)\n          );\n          const asyncValidationIsFinishedPromise = Promise.all(asyncValidatedSubValues).then(\n            (flatValidatedValues: boolean[]) => {\n              const validationResult = flatValidatedValues.every((isValid: boolean) => isValid);\n              if (!validationResult) {\n                const [type, message] = this.createValidationError(object, value, metadata, customConstraintMetadata);\n                error.constraints[type] = message;\n                if (metadata.context) {\n                  if (!error.contexts) {\n                    error.contexts = {};\n                  }\n                  error.contexts[type] = Object.assign(error.contexts[type] || {}, metadata.context);\n                }\n              }\n            }\n          );\n\n          this.awaitingPromises.push(asyncValidationIsFinishedPromise);\n\n          return;\n        }\n\n        const validationResult = validatedSubValues.every((isValid: boolean) => isValid);\n        if (!validationResult) {\n          const [type, message] = this.createValidationError(object, value, metadata, customConstraintMetadata);\n          error.constraints[type] = message;\n        }\n      });\n    });\n  }\n\n  private nestedValidations(value: any, metadatas: ValidationMetadata[], error: ValidationError): void {\n    if (value === void 0) {\n      return;\n    }\n\n    metadatas.forEach(metadata => {\n      if (metadata.type !== ValidationTypes.NESTED_VALIDATION && metadata.type !== ValidationTypes.PROMISE_VALIDATION) {\n        return;\n      } else if (\n        this.validatorOptions &&\n        this.validatorOptions.stopAtFirstError &&\n        Object.keys(error.constraints || {}).length > 0\n      ) {\n        return;\n      }\n\n      if (Array.isArray(value) || value instanceof Set || value instanceof Map) {\n        // Treats Set as an array - as index of Set value is value itself and it is common case to have Object as value\n        const arrayLikeValue = value instanceof Set ? Array.from(value) : value;\n        arrayLikeValue.forEach((subValue: any, index: any) => {\n          this.performValidations(value, subValue, index.toString(), [], metadatas, error.children);\n        });\n      } else if (value instanceof Object) {\n        const targetSchema = typeof metadata.target === 'string' ? metadata.target : metadata.target.name;\n        this.execute(value, targetSchema, error.children);\n      } else {\n        const [type, message] = this.createValidationError(metadata.target as object, value, metadata);\n        error.constraints[type] = message;\n      }\n    });\n  }\n\n  private mapContexts(object: object, value: any, metadatas: ValidationMetadata[], error: ValidationError): void {\n    return metadatas.forEach(metadata => {\n      if (metadata.context) {\n        let customConstraint;\n        if (metadata.type === ValidationTypes.CUSTOM_VALIDATION) {\n          const customConstraints = this.metadataStorage.getTargetValidatorConstraints(metadata.constraintCls);\n          customConstraint = customConstraints[0];\n        }\n\n        const type = this.getConstraintType(metadata, customConstraint);\n\n        if (error.constraints[type]) {\n          if (!error.contexts) {\n            error.contexts = {};\n          }\n\n          error.contexts[type] = Object.assign(error.contexts[type] || {}, metadata.context);\n        }\n      }\n    });\n  }\n\n  private createValidationError(\n    object: object,\n    value: any,\n    metadata: ValidationMetadata,\n    customValidatorMetadata?: ConstraintMetadata\n  ): [string, string] {\n    const targetName = object.constructor ? (object.constructor as any).name : undefined;\n    const type = this.getConstraintType(metadata, customValidatorMetadata);\n    const validationArguments: ValidationArguments = {\n      targetName: targetName,\n      property: metadata.propertyName,\n      object: object,\n      value: value,\n      constraints: metadata.constraints,\n    };\n\n    let message = metadata.message || '';\n    if (\n      !metadata.message &&\n      (!this.validatorOptions || (this.validatorOptions && !this.validatorOptions.dismissDefaultMessages))\n    ) {\n      if (customValidatorMetadata && customValidatorMetadata.instance.defaultMessage instanceof Function) {\n        message = customValidatorMetadata.instance.defaultMessage(validationArguments);\n      }\n    }\n\n    const messageString = ValidationUtils.replaceMessageSpecialTokens(message, validationArguments);\n    return [type, messageString];\n  }\n\n  private getConstraintType(metadata: ValidationMetadata, customValidatorMetadata?: ConstraintMetadata): string {\n    const type = customValidatorMetadata && customValidatorMetadata.name ? customValidatorMetadata.name : metadata.type;\n    return type;\n  }\n}\n"]}