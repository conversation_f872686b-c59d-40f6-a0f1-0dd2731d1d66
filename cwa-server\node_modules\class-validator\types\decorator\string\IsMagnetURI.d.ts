import { ValidationOptions } from '../ValidationOptions';
export declare const IS_MAGNET_URI = "isMagnetURI";
/**
 * Check if the string is a magnet uri format.
 * If given value is not a string, then it returns false.
 */
export declare function isMagnetURI(value: unknown): boolean;
/**
 * Check if the string is a magnet uri format.
 * If given value is not a string, then it returns false.
 */
export declare function IsMagnetURI(validationOptions?: ValidationOptions): PropertyDecorator;
