{"version": 3, "file": "stripIddPrefix.js", "names": ["CAPTURING_DIGIT_PATTERN", "RegExp", "VALID_DIGITS", "stripIddPrefix", "number", "country", "callingCode", "metadata", "countryMetadata", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "IDDPrefixPattern", "IDDPrefix", "search", "slice", "match", "length", "matchedGroups"], "sources": ["../../source/helpers/stripIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport { VALID_DIGITS } from '../constants.js'\r\n\r\nconst CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])')\r\n\r\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\r\n\tif (!country) {\r\n\t\treturn\r\n\t}\r\n\t// Check if the number is IDD-prefixed.\r\n\tconst countryMetadata = new Metadata(metadata)\r\n\tcountryMetadata.selectNumberingPlan(country, callingCode)\r\n\tconst IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix())\r\n\tif (number.search(IDDPrefixPattern) !== 0) {\r\n\t\treturn\r\n\t}\r\n\t// Strip IDD prefix.\r\n\tnumber = number.slice(number.match(IDDPrefixPattern)[0].length)\r\n\t// If there're any digits after an IDD prefix,\r\n\t// then those digits are a country calling code.\r\n\t// Since no country code starts with a `0`,\r\n\t// the code below validates that the next digit (if present) is not `0`.\r\n\tconst matchedGroups = number.match(CAPTURING_DIGIT_PATTERN)\r\n\tif (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\r\n\t\tif (matchedGroups[1] === '0') {\r\n\t\t\treturn\r\n\t\t}\r\n\t}\r\n\treturn number\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEA,IAAMA,uBAAuB,GAAG,IAAIC,MAAJ,CAAW,OAAOC,uBAAP,GAAsB,IAAjC,CAAhC;;AAEe,SAASC,cAAT,CAAwBC,MAAxB,EAAgCC,OAAhC,EAAyCC,WAAzC,EAAsDC,QAAtD,EAAgE;EAC9E,IAAI,CAACF,OAAL,EAAc;IACb;EACA,CAH6E,CAI9E;;;EACA,IAAMG,eAAe,GAAG,IAAIC,oBAAJ,CAAaF,QAAb,CAAxB;EACAC,eAAe,CAACE,mBAAhB,CAAoCL,OAApC,EAA6CC,WAA7C;EACA,IAAMK,gBAAgB,GAAG,IAAIV,MAAJ,CAAWO,eAAe,CAACI,SAAhB,EAAX,CAAzB;;EACA,IAAIR,MAAM,CAACS,MAAP,CAAcF,gBAAd,MAAoC,CAAxC,EAA2C;IAC1C;EACA,CAV6E,CAW9E;;;EACAP,MAAM,GAAGA,MAAM,CAACU,KAAP,CAAaV,MAAM,CAACW,KAAP,CAAaJ,gBAAb,EAA+B,CAA/B,EAAkCK,MAA/C,CAAT,CAZ8E,CAa9E;EACA;EACA;EACA;;EACA,IAAMC,aAAa,GAAGb,MAAM,CAACW,KAAP,CAAaf,uBAAb,CAAtB;;EACA,IAAIiB,aAAa,IAAIA,aAAa,CAAC,CAAD,CAAb,IAAoB,IAArC,IAA6CA,aAAa,CAAC,CAAD,CAAb,CAAiBD,MAAjB,GAA0B,CAA3E,EAA8E;IAC7E,IAAIC,aAAa,CAAC,CAAD,CAAb,KAAqB,GAAzB,EAA8B;MAC7B;IACA;EACD;;EACD,OAAOb,MAAP;AACA"}